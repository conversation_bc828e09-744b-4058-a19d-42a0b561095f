//
//  Preferences.h
//  PAPreferencesSample
//
//  Created by <PERSON> on 29/04/2014.
//  Copyright (c) 2014 Peer Assembly. All rights reserved.
//

#import "PAPreferences.h"
#import <UIKit/UIKit.h>

// Ensure we get an error if we forget to add @dynamic for each property
#pragma clang diagnostic push
#pragma clang diagnostic error "-Wobjc-missing-property-synthesis"


@interface Preferences : PAPreferences {
    
}

//@property (nonatomic, assign) NSInteger filterDis;

#pragma mark - <  数据缓存 >
@property (nonatomic, assign) NSArray <NSString *>*deleteArray;
@property (nonatomic, assign) NSArray <NSString *>*archiveArray;
@property (nonatomic, assign) NSString *lastLocalIdentifier;
@property (nonatomic, assign) NSString *lastAlbumLocalIdentifier;

#pragma mark - <  设置选项 >
@property (nonatomic, assign) BOOL autoPlayEnabled;
@property (nonatomic, assign) NSInteger autoPlayInterval; // 自动播放间隔（秒）
@property (nonatomic, assign) NSInteger swipeDirection; // 0:左右滑动切换 , 1: 上下滑动切换

@property (nonatomic, assign) CGFloat windowSize_Width;
@property (nonatomic, assign) CGFloat windowSize_Height;

#pragma mark - <  Widget相册选择 >
@property (nonatomic, assign) NSArray <NSString *>*selectedAlbumIdentifiers; // Widget选中的相册标识符数组
@end
#pragma clang diagnostic pop
